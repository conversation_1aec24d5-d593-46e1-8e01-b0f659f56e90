defmodule Mix.Tasks.Drops.Relation.GenSchemasIntegrationTest do
  use ExUnit.Case, async: false

  import ExUnit.CaptureIO

  @sample_app_path Path.join([__DIR__, "..", "..", "sample_app"])
  @schemas_path Path.join([@sample_app_path, "lib", "sample_app", "schemas"])

  setup do
    # Clean up any existing schema files before each test
    if File.exists?(@schemas_path) do
      File.rm_rf!(@schemas_path)
    end

    # Ensure the schemas directory exists
    File.mkdir_p!(@schemas_path)

    # Change to sample_app directory for mix tasks
    original_cwd = File.cwd!()
    File.cd!(@sample_app_path)

    on_exit(fn ->
      File.cd!(original_cwd)
      # Clean up after test
      if File.exists?(@schemas_path) do
        File.rm_rf!(@schemas_path)
      end
    end)

    :ok
  end

  describe "gen_schemas mix task integration" do
    test "generates schema files for all tables with --yes option" do
      # Run the mix task with --yes to avoid prompts
      output =
        capture_io(fn ->
          Mix.Task.run("drops.relation.gen_schemas", [
            "--app",
            "SampleApp",
            "--repo",
            "SampleApp.Repo",
            "--namespace",
            "SampleApp.Schemas",
            "--yes"
          ])
        end)

      # Verify the task ran successfully
      assert output =~ "Creating or updating schema"
      assert output =~ "SampleApp.Schemas.User"
      assert output =~ "SampleApp.Schemas.Post"
      assert output =~ "SampleApp.Schemas.Comment"

      # Verify schema files were created
      user_file = Path.join(@schemas_path, "user.ex")
      post_file = Path.join(@schemas_path, "post.ex")
      comment_file = Path.join(@schemas_path, "comment.ex")

      assert File.exists?(user_file)
      assert File.exists?(post_file)
      assert File.exists?(comment_file)

      # Verify user schema content
      user_content = File.read!(user_file)
      assert user_content =~ "defmodule SampleApp.Schemas.User do"
      assert user_content =~ "use Ecto.Schema"
      refute user_content =~ "import Ecto.Schema"
      assert user_content =~ "schema \"users\" do"
      assert user_content =~ "field :email, :string"
      assert user_content =~ "field :first_name, :string"
      assert user_content =~ "field :age, :integer"
      assert user_content =~ "field :is_active, :boolean"
      assert user_content =~ "timestamps()"

      # Verify post schema content with foreign key
      post_content = File.read!(post_file)
      assert post_content =~ "defmodule SampleApp.Schemas.Post do"
      assert post_content =~ "use Ecto.Schema"
      refute post_content =~ "import Ecto.Schema"
      assert post_content =~ "schema \"posts\" do"
      assert post_content =~ "field :title, :string"
      assert post_content =~ "field :body, :string"
      assert post_content =~ "field :user_id, :id"
      assert post_content =~ "timestamps()"

      # Verify comment schema content with multiple foreign keys
      comment_content = File.read!(comment_file)
      assert comment_content =~ "defmodule SampleApp.Schemas.Comment do"
      assert comment_content =~ "use Ecto.Schema"
      refute comment_content =~ "import Ecto.Schema"
      assert comment_content =~ "schema \"comments\" do"
      assert comment_content =~ "field :body, :string"
      assert comment_content =~ "field :user_id, :id"
      assert comment_content =~ "field :post_id, :id"
      assert comment_content =~ "timestamps()"
    end

    test "generates schema for specific table only" do
      # Run the mix task for users table only
      output =
        capture_io(fn ->
          Mix.Task.run("drops.relation.gen_schemas", [
            "--app",
            "SampleApp",
            "--repo",
            "SampleApp.Repo",
            "--namespace",
            "SampleApp.Schemas",
            "--tables",
            "users",
            "--yes"
          ])
        end)

      # Verify only user schema was created
      assert output =~ "SampleApp.Schemas.User"
      refute output =~ "SampleApp.Schemas.Post"
      refute output =~ "SampleApp.Schemas.Comment"

      user_file = Path.join(@schemas_path, "user.ex")
      post_file = Path.join(@schemas_path, "post.ex")
      comment_file = Path.join(@schemas_path, "comment.ex")

      assert File.exists?(user_file)
      refute File.exists?(post_file)
      refute File.exists?(comment_file)
    end

    test "updates existing schema file in sync mode" do
      # First, create an initial schema file with custom content
      user_file = Path.join(@schemas_path, "user.ex")

      initial_content = """
      defmodule SampleApp.Schemas.User do
        use Ecto.Schema

        schema "users" do
          field :email, :string
          field :first_name, :string
          # This is a custom comment that should be preserved
          timestamps()
        end

        # Custom function that should be preserved
        def full_name(%__MODULE__{first_name: first, last_name: last}) do
          "\#{first} \#{last}"
        end
      end
      """

      File.write!(user_file, initial_content)

      # Run the mix task in sync mode
      output =
        capture_io(fn ->
          Mix.Task.run("drops.relation.gen_schemas", [
            "--app",
            "SampleApp",
            "--repo",
            "SampleApp.Repo",
            "--namespace",
            "SampleApp.Schemas",
            "--tables",
            "users",
            "--sync",
            "--yes"
          ])
        end)

      # Verify the task ran in sync mode
      assert output =~ "Creating or updating schema"

      # Verify the file was updated
      updated_content = File.read!(user_file)

      # Should preserve custom function
      assert updated_content =~ "def full_name"

      # Should preserve custom comment
      assert updated_content =~ "This is a custom comment"

      # Should have updated schema fields
      assert updated_content =~ "field :last_name, :string"
      assert updated_content =~ "field :age, :integer"
      assert updated_content =~ "field :is_active, :boolean"

      # Should not have import Ecto.Schema
      refute updated_content =~ "import Ecto.Schema"
    end

    test "generated schemas are valid Ecto.Schema modules" do
      # Generate schemas
      capture_io(fn ->
        Mix.Task.run("drops.relation.gen_schemas", [
          "--app",
          "SampleApp",
          "--repo",
          "SampleApp.Repo",
          "--namespace",
          "SampleApp.Schemas",
          "--yes"
        ])
      end)

      # Load and verify each generated schema module
      user_file = Path.join(@schemas_path, "user.ex")
      post_file = Path.join(@schemas_path, "post.ex")
      comment_file = Path.join(@schemas_path, "comment.ex")

      # Compile and load the modules to verify they're valid
      [{user_module, _}] = Code.compile_file(user_file)
      [{post_module, _}] = Code.compile_file(post_file)
      [{comment_module, _}] = Code.compile_file(comment_file)

      # Verify they implement Ecto.Schema behavior
      assert function_exported?(user_module, :__schema__, 1)
      assert function_exported?(post_module, :__schema__, 1)
      assert function_exported?(comment_module, :__schema__, 1)

      # Verify schema metadata
      assert user_module.__schema__(:source) == "users"
      assert post_module.__schema__(:source) == "posts"
      assert comment_module.__schema__(:source) == "comments"

      # Verify fields exist
      user_fields = user_module.__schema__(:fields)
      assert :email in user_fields
      assert :first_name in user_fields
      assert :last_name in user_fields
      assert :age in user_fields

      post_fields = post_module.__schema__(:fields)
      assert :title in post_fields
      assert :body in post_fields
      assert :user_id in post_fields

      comment_fields = comment_module.__schema__(:fields)
      assert :body in comment_fields
      assert :user_id in comment_fields
      assert :post_id in comment_fields
    end
  end
end
